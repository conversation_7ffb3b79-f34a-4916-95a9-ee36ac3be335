"use client"

import React from 'react';
import { <PERSON><PERSON>hart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer, type TooltipProps } from 'recharts';
import type { Expense } from '../../data/mockExpenses'; // Relative path
import type { NameType, ValueType } from 'recharts/types/component/DefaultTooltipContent';

interface ExpenseChartProps {
  data: Expense[]; // Expecting pre-aggregated data if needed, or raw data to be processed
}

// Helper to aggregate data for the chart if needed
// For simplicity, this chart will display individual expenses or pre-aggregated data.
// Aggregation logic (e.g., by month, category) should ideally be done before passing data to this component.

// @ts-expect-error
const CustomTooltip = ({ active, payload }: TooltipProps<ValueType, NameType>) => {
  if (active && payload && payload.length) {
    const data = payload[0].payload; // Access the full data point
    return (
      <div className="bg-background border border-border shadow-md p-2 rounded-md">
        <p className="label font-semibold">{`Date: ${new Date(data.date).toLocaleDateString()}`}</p>
        <p className="intro">{`Category: ${data.category}`}</p>
        <p className="desc">{`Amount: $${data.amount.toFixed(2)}`}</p>
        {data.description && <p className="text-xs text-muted-foreground">{`Description: ${data.description}`}</p>}
      </div>
    );
  }

  return null;
};


const ExpenseChart: React.FC<ExpenseChartProps> = ({ data }) => {
  if (!data || data.length === 0) {
    return <div className="text-center p-4">No data available for chart.</div>;
  }

  // Assuming data is sorted by date for a time-series view if not aggregated
  // Or, if aggregated, 'date' might represent a period (e.g., month name)
  const chartData = data.map(item => ({
    ...item,
    // Ensure date is in a format Recharts can use or is a string label
    // For this example, we'll use the date as is, assuming it's suitable for XAxis
    // Recharts typically handles Date objects well for time axes, but string labels are also common.
    // If using string dates like "YYYY-MM-DD", ensure they are sorted.
    name: new Date(item.date).toLocaleDateString(undefined, { month: 'short', day: 'numeric' }) // Or item.category if charting by category
  }));


  return (
    <ResponsiveContainer width="100%" height={400}>
      <BarChart
        data={chartData}
        margin={{
          top: 5,
          right: 30,
          left: 20,
          bottom: 5,
        }}
      >
        <CartesianGrid strokeDasharray="3 3" />
        <XAxis dataKey="name" />
        <YAxis />
        <Tooltip content={<CustomTooltip />} />
        <Legend />
        <Bar dataKey="amount" fill="hsl(var(--primary))" />
        {/* Add more bars here if comparing multiple values per x-axis point */}
      </BarChart>
    </ResponsiveContainer>
  );
};

export default ExpenseChart;
